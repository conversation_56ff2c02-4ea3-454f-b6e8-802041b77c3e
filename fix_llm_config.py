#!/usr/bin/env python3
import sqlite3

def fix_llm_config():
    # 连接数据库
    conn = sqlite3.connect('backend/questions.db')
    cursor = conn.cursor()
    
    print("=== 修复LLM配置 ===")
    
    # 更新模型名称和Base URL
    cursor.execute("""
        UPDATE llm_configs 
        SET model_name = ?, base_url = ?, updated_time = datetime('now')
        WHERE id = 1
    """, ("deepseek-chat", "https://api.deepseek.com/v1"))
    
    conn.commit()
    
    # 验证更新结果
    cursor.execute("""
        SELECT id, config_name, base_url, model_name, is_active
        FROM llm_configs 
        WHERE id = 1
    """)
    
    config = cursor.fetchone()
    if config:
        print(f"配置已更新:")
        print(f"  ID: {config[0]}")
        print(f"  配置名称: {config[1]}")
        print(f"  Base URL: {config[2]}")
        print(f"  模型名称: {config[3]}")
        print(f"  状态: {'激活' if config[4] == 1 else '未激活'}")
        print("修复完成！")
    else:
        print("配置不存在")
    
    conn.close()

if __name__ == "__main__":
    fix_llm_config()
