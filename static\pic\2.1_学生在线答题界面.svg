<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <rect width="1920" height="1080" fill="#f8fafc"/>
  <rect x="0" y="0" width="1920" height="80" fill="#1d4ed8"/>
  <text x="40" y="50" font-family="Arial" font-size="26" fill="#fff" font-weight="700">学生端 · 在线答题</text>

  <rect x="60" y="120" width="1800" height="900" fill="#fff" stroke="#e5e7eb" rx="14"/>

  <!-- 题干 -->
  <text x="100" y="190" font-family="Arial" font-size="20" fill="#0f172a" font-weight="700">单选题 1/10</text>
  <text x="100" y="230" font-family="Arial" font-size="18" fill="#334155">以下Python代码的输出结果是？</text>
  <rect x="100" y="250" width="1720" height="140" fill="#f1f5f9" stroke="#e2e8f0" rx="10"/>
  <text x="120" y="285" font-family="monospace" font-size="16" fill="#111827">x = 7</text>
  <text x="120" y="315" font-family="monospace" font-size="16" fill="#111827">if x % 2 == 1: print("odd")</text>

  <!-- 选项 -->
  <rect x="100" y="420" width="1720" height="70" fill="#ffffff" stroke="#dbeafe" rx="10"/>
  <circle cx="130" cy="455" r="10" fill="#3b82f6"/>
  <text x="160" y="460" font-family="Arial" font-size="18" fill="#0f172a">A. odd</text>

  <rect x="100" y="510" width="1720" height="70" fill="#ffffff" stroke="#e5e7eb" rx="10"/>
  <circle cx="130" cy="545" r="10" fill="#e5e7eb"/>
  <text x="160" y="550" font-family="Arial" font-size="18" fill="#0f172a">B. even</text>

  <rect x="100" y="600" width="1720" height="70" fill="#ffffff" stroke="#e5e7eb" rx="10"/>
  <circle cx="130" cy="635" r="10" fill="#e5e7eb"/>
  <text x="160" y="640" font-family="Arial" font-size="18" fill="#0f172a">C. 报错</text>

  <rect x="100" y="690" width="1720" height="70" fill="#ffffff" stroke="#e5e7eb" rx="10"/>
  <circle cx="130" cy="725" r="10" fill="#e5e7eb"/>
  <text x="160" y="730" font-family="Arial" font-size="18" fill="#0f172a">D. 无输出</text>

  <!-- 底部操作 -->
  <rect x="100" y="810" width="200" height="54" fill="#3b82f6" rx="10"/>
  <text x="200" y="845" font-family="Arial" font-size="18" fill="#fff" font-weight="700" text-anchor="middle">提交</text>
  <text x="1500" y="845" font-family="Arial" font-size="16" fill="#334155">剩余时间：09:45</text>
</svg>