@echo off
echo ====================================
echo    Unit Choice Quiz System - Startup
echo ====================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found, please install Python first
    pause
    exit /b 1
)

REM Check and install backend dependencies
echo [INFO] Installing backend dependencies...
cd backend
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)
echo [INFO] Backend dependencies installed successfully

REM Go back to project root for backend server
cd ..

REM Start backend server
echo [INFO] Starting backend API server (port 8000)...
start "Backend Server" cmd /k "cd backend && python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload"

REM Wait for backend to start
echo [INFO] Waiting for backend server to start...
timeout /t 3 /nobreak >nul

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js not found, please install Node.js first
    pause
    exit /b 1
)

REM Switch to frontend directory
cd frontend

REM Install frontend dependencies
echo [INFO] Installing frontend dependencies...
npm install
if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)
echo [INFO] Frontend dependencies installed successfully

REM Start frontend development server
echo [INFO] Starting frontend development server (port 3000)...
echo [DEBUG] Frontend directory: %~dp0frontend
echo [DEBUG] Current directory: %cd%

REM Check if frontend directory exists
if not exist "%~dp0frontend" (
    echo [ERROR] Frontend directory not found: %~dp0frontend
    pause
    exit /b 1
)

REM Start frontend in a new window with better error handling
start "Frontend Server" cmd /k "cd /d "%~dp0frontend" && echo Starting frontend... && npm run dev || (echo Frontend failed to start && pause)"

REM Wait for frontend to start
echo [INFO] Waiting for frontend server to start...
timeout /t 8 /nobreak >nul

REM Check if frontend is running
echo [INFO] Checking if frontend is accessible...
curl -s http://localhost:3000 >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Frontend may not be fully started yet. Please check the Frontend Server window.
) else (
    echo [INFO] Frontend is accessible at http://localhost:3000
)

echo.
echo ====================================
echo           Startup Complete!
echo ====================================
echo Frontend: http://localhost:3000
echo Backend: http://localhost:8000
echo Admin: http://localhost:3000/admin
echo.
echo Press any key to open browser...
pause >nul

REM Open browser
start http://localhost:3000

echo.
echo Services started, press any key to exit this window...
echo (Note: Closing this window will not stop the servers)
pause >nul