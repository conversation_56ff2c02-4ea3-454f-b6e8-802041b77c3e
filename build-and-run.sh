#!/bin/bash

# 构建并运行本地Docker镜像的脚本

echo "=== 构建本地Docker镜像 ==="

# 停止并删除现有容器
echo "停止现有容器..."
docker stop unitechoose8 2>/dev/null || true
docker rm unitechoose8 2>/dev/null || true

# 构建新镜像
echo "构建Docker镜像..."
docker build -t unitechoose-local:latest .

if [ $? -eq 0 ]; then
    echo "✅ 镜像构建成功"
    
    # 创建数据目录
    mkdir -p /d/docker/unitechoose/data
    
    # 运行容器
    echo "启动容器..."
    docker run -d \
        --name unitechoose8 \
        -p 7605:8000 \
        -v /d/docker/unitechoose/data:/app/data \
        --network=mynet \
        --restart unless-stopped \
        unitechoose-local:latest
    
    if [ $? -eq 0 ]; then
        echo "✅ 容器启动成功"
        echo "查看容器日志："
        docker logs -f unitechoose8
    else
        echo "❌ 容器启动失败"
        exit 1
    fi
else
    echo "❌ 镜像构建失败"
    exit 1
fi
