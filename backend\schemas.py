from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

# 题目相关模型
class QuestionBase(BaseModel):
    question_content: str
    option_a: str
    option_b: str
    option_c: str
    option_d: str
    answer: str
    knowledge_point: str
    explanation: Optional[str] = None
    wrong_analysis: Optional[str] = None
    difficulty_level: Optional[str] = None  # 难度等级
    topic_category: Optional[str] = None  # 主题分类

class QuestionCreate(BaseModel):
    questionContent: str
    optionA: str
    optionB: str
    optionC: str
    optionD: str
    answer: str
    knowledgePoint: str
    explanation: Optional[str] = None
    wrongAnalysis: Optional[str] = None
    questionBankId: Optional[int] = None
    
    # 转换为数据库字段格式
    @property
    def question_content(self):
        return self.questionContent
    
    @property
    def option_a(self):
        return self.optionA
    
    @property
    def option_b(self):
        return self.optionB
    
    @property
    def option_c(self):
        return self.optionC
    
    @property
    def option_d(self):
        return self.optionD
    
    @property
    def knowledge_point(self):
        return self.knowledgePoint
    
    @property
    def wrong_analysis(self):
        return self.wrongAnalysis
    
    @property
    def question_bank_id(self):
        return self.questionBankId

class Question(QuestionBase):
    id: int
    created_time: datetime
    updated_time: datetime
    
    class Config:
        from_attributes = True

# 学生答案模型
class StudentAnswer(BaseModel):
    questionId: int
    selectedAnswer: str

# 提交答案模型
class SubmissionCreate(BaseModel):
    student_name: str
    class_id: int
    answers: List[StudentAnswer]

# 提交记录模型
class SubmissionBase(BaseModel):
    student_name: str
    score: int
    submission_date: str
    class_id: int
    client_ip: Optional[str] = None
    total_questions: Optional[int] = 0  # 总题目数
    correct_answers: Optional[int] = 0  # 正确答案数
    completion_time: Optional[int] = None  # 完成时间（秒）

class Submission(SubmissionBase):
    id: int
    submission_time: datetime
    
    class Config:
        from_attributes = True

# 提交详情模型
class SubmissionDetailBase(BaseModel):
    submission_id: int
    question_id: int
    selected_answer: str
    is_correct: Optional[int] = 0  # 是否正确：0-错误，1-正确
    answer_time: Optional[datetime] = None  # 答题时间

class SubmissionDetailCreate(SubmissionDetailBase):
    pass

class SubmissionDetail(SubmissionDetailBase):
    id: int
    
    class Config:
        from_attributes = True

# 答题结果详情模型
class QuestionResult(BaseModel):
    questionContent: str
    yourAnswer: str
    correctAnswer: str
    knowledgePoint: str
    explanation: Optional[str] = None
    wrongAnalysis: Optional[str] = None
    isCorrect: bool

# 提交结果模型
class SubmissionResult(BaseModel):
    score: int
    full_results: List[QuestionResult]

# 答题结果详情模型
class ScoreRecord(BaseModel):
    submission_id: int
    student_name: str
    score: int
    submission_time: str
    client_ip: Optional[str] = None
    
    class Config:
        from_attributes = True

# 管理员题目批量导入模型
class QuestionBatch(BaseModel):
    questions: List[QuestionCreate]
    class_id: int

# 学情分析相关模型
class KnowledgePointAnalysis(BaseModel):
    knowledge_point: str
    total_questions: int
    correct_count: int
    incorrect_count: int
    accuracy_rate: float
    common_wrong_answers: List[str]

class StudentPerformance(BaseModel):
    student_name: str
    score: int
    accuracy_rate: float
    weak_knowledge_points: List[str]
    submission_time: str

class ClassAnalysis(BaseModel):
    class_id: int
    total_students: int
    average_score: float
    score_distribution: dict  # 分数段分布
    knowledge_point_analysis: List[KnowledgePointAnalysis]
    top_performers: List[StudentPerformance]
    need_help_students: List[StudentPerformance]
    common_mistakes: List[str]

# API响应模型
class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[dict] = None

# 题库相关模型
class QuestionBankBase(BaseModel):
    name: str
    description: Optional[str] = None
    created_by: Optional[str] = None
    is_active: Optional[int] = 1

class QuestionBankCreate(QuestionBankBase):
    pass

class QuestionBankUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[int] = None

class QuestionBank(QuestionBankBase):
    id: int
    created_time: datetime
    updated_time: datetime
    question_count: int
    
    class Config:
        from_attributes = True

# 题库详情模型（包含题目列表）
class QuestionBankDetail(QuestionBank):
    questions: List[Question] = []

# 大语言模型配置相关模型
class LLMConfigBase(BaseModel):
    model_config = {"protected_namespaces": ()}
    
    config_name: str
    api_key: str
    base_url: str
    model_name: str
    is_active: Optional[int] = 0

class LLMConfigCreate(LLMConfigBase):
    pass

class LLMConfigUpdate(BaseModel):
    config_name: Optional[str] = None
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model_name: Optional[str] = None
    is_active: Optional[int] = None

class LLMConfig(LLMConfigBase):
    id: int
    created_time: datetime
    updated_time: datetime
    
    class Config:
        from_attributes = True

# 教师数据看板相关模型
class ExportRequest(BaseModel):
    class_id: int
    format: str = "excel"  # excel 或 pdf

# AI生成题目请求模型
class AIGenerateRequest(BaseModel):
    knowledge_point: str
    count: int = 5
    difficulty: Optional[str] = None  # easy/medium/hard
    topic: Optional[str] = None  # 主题分类
    batch_mode: bool = False  # 是否批量生成模式
    prompts: Optional[List[str]] = None  # 批量模式下的提示列表

# 班级配置相关模型
class ClassConfigBase(BaseModel):
    current_class_id: int
    class_name: Optional[str] = None

class ClassConfigCreate(ClassConfigBase):
    pass

class ClassConfigUpdate(BaseModel):
    current_class_id: Optional[int] = None
    class_name: Optional[str] = None

class ClassConfig(ClassConfigBase):
    id: int
    updated_time: datetime
    
    class Config:
        from_attributes = True