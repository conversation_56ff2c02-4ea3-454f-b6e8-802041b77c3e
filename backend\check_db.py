import sqlite3

# 连接到数据库
conn = sqlite3.connect('questions.db')
cursor = conn.cursor()

# 检查Gemini配置详情
print("=== Gemini配置检查 ===")
cursor.execute("SELECT config_name, model_name, base_url, is_active FROM llm_configs WHERE config_name LIKE '%gemini%'")
result = cursor.fetchone()
if result:
    print(f'配置名称: {result[0]}')
    print(f'模型名称: {result[1]}')
    print(f'Base URL: {result[2]}')
    print(f'激活状态: {"已激活" if result[3] else "未激活"}')
else:
    print('未找到Gemini配置')

print("\n=== 所有LLM配置 ===")
cursor.execute("SELECT id, config_name, model_name, base_url, is_active FROM llm_configs")
records = cursor.fetchall()
for record in records:
    print(f"ID: {record[0]}, 名称: {record[1]}, 模型: {record[2]}, URL: {record[3]}, 激活: {'是' if record[4] else '否'}")

print("\n=== 题库数据检查 ===")
# 检查题库表是否存在
cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='question_banks'")
if cursor.fetchone():
    print("题库表存在")
    
    # 检查所有题库
    cursor.execute("SELECT id, name, is_active, question_count FROM question_banks")
    banks = cursor.fetchall()
    if banks:
        print(f"共有 {len(banks)} 个题库:")
        for bank in banks:
            print(f"  ID: {bank[0]}, 名称: {bank[1]}, 激活: {'是' if bank[2] else '否'}, 题目数: {bank[3]}")
        
        # 检查激活的题库
        cursor.execute("SELECT id, name FROM question_banks WHERE is_active = 1")
        active_bank = cursor.fetchone()
        if active_bank:
            print(f"\n当前激活题库: ID={active_bank[0]}, 名称={active_bank[1]}")
        else:
            print("\n没有激活的题库")
    else:
        print("没有题库数据")
else:
    print("题库表不存在")

print("\n=== 题目数据检查 ===")
cursor.execute("SELECT COUNT(*) FROM questions")
question_count = cursor.fetchone()[0]
print(f"总题目数: {question_count}")

if question_count > 0:
    cursor.execute("SELECT question_bank_id, COUNT(*) FROM questions GROUP BY question_bank_id")
    bank_questions = cursor.fetchall()
    print("各题库题目分布:")
    for bq in bank_questions:
        if bq[0]:
            cursor.execute("SELECT name FROM question_banks WHERE id = ?", (bq[0],))
            bank_name = cursor.fetchone()
            bank_name = bank_name[0] if bank_name else "未知题库"
            print(f"  题库 {bq[0]} ({bank_name}): {bq[1]} 道题目")
        else:
            print(f"  无题库归属: {bq[1]} 道题目")

# 关闭连接
conn.close()