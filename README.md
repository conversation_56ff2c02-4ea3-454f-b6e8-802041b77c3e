# 高中信息技术在线选择题练习平台

一个为高中信息技术课设计的完整前后端分离Web应用，支持学生在线答题和教师后台管理。

## 项目简介

本项目是一个现代化的在线选择题练习和管理平台，专为高中信息技术课程设计。学生可以通过简洁的界面进行在线答题，教师可以通过后台管理界面设置题目和查看学生成绩。

## 技术栈

### 后端
- **Python 3.10+** - 编程语言
- **FastAPI** - Web框架
- **SQLAlchemy** - ORM数据库操作
- **SQLite** - 数据库
- **Pydantic** - 数据验证
- **Uvicorn** - ASGI服务器

### 前端
- **Vue.js 3** - 前端框架
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端
- **Vite** - 构建工具

### 部署
- **Docker** - 容器化部署
- **多阶段构建** - 优化镜像大小

## 功能列表

### 学生端功能
- ✅ **用户信息录入** - 输入姓名和选择班级
- ✅ **在线答题** - 一次性展示所有题目，支持单选答题
- ✅ **成绩查看** - 显示得分和详细的答题结果
- ✅ **答案解析** - 查看正确答案和知识点解析
- ✅ **错题标记** - 错误答案红色标记，正确答案绿色显示

### 管理端功能
- ✅ **安全认证** - HTTP Basic Auth认证（用户名：admin，密码：admin123）
- ✅ **题目管理** - 通过JSON格式批量导入题目
- ✅ **班级管理** - 支持1-17班级选择
- ✅ **成绩统计** - 查看当天各班级学生提交记录
- ✅ **实时更新** - 题目更新后立即生效

## 项目结构

```
unitechoose/
├── backend/                 # 后端应用
│   ├── main.py             # FastAPI主应用
│   ├── database.py         # 数据库连接和模型
│   ├── crud.py             # 数据库操作函数
│   ├── schemas.py          # Pydantic数据模型
│   ├── requirements.txt    # Python依赖
│   └── questions.db        # SQLite数据库文件
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── components/    # Vue组件
│   │   │   ├── Home.vue   # 首页组件
│   │   │   ├── Quiz.vue   # 答题页组件
│   │   │   └── Result.vue # 结果页组件
│   │   ├── main.js        # 应用入口
│   │   └── App.vue        # 根组件
│   ├── public/            # 静态资源
│   ├── index.html         # HTML模板
│   ├── package.json       # 项目配置
│   └── vite.config.js     # Vite配置
├── Dockerfile             # Docker构建文件
└── README.md              # 项目文档
```

## 快速开始

### 环境要求
- Python 3.10+
- Node.js 16+
- npm 或 yarn

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd unitechoose
```

2. **后端设置**
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装依赖
pip install -r backend/requirements.txt

# 导入示例题目（可选）
python backend/import_questions.py
```

3. **前端设置**
```bash
cd frontend
npm install
```

### 运行应用

#### 方法一：使用启动脚本（推荐）
```bash
# Windows用户可以双击运行start.bat文件
# 或在PowerShell中运行：
.\start.bat
```

#### 方法二：手动启动

1. **启动后端服务**
```bash
# 在项目根目录
uvicorn backend.main:app --reload --host 0.0.0.0 --port 8001
```

2. **启动前端开发服务器**
```bash
# 在frontend目录
npm run dev
```

3. **访问应用**
- 学生端：http://localhost:3000
- 后端API：http://localhost:8001
- 管理端：http://localhost:8001/admin
  - 用户名：admin
  - 密码：admin123

## 本地部署指南

### 方式一：Docker部署（推荐）

#### 前置要求
- 安装 [Docker](https://www.docker.com/get-started)
- 确保Docker服务正在运行

#### 部署步骤

1. **克隆仓库**
   ```bash
   git clone <repository-url>
   cd unitechoose
   ```

2. **构建Docker镜像**
   ```bash
   docker build -t quiz-app .
   ```

3. **运行Docker容器**
   ```bash
   docker run -d -p 8000:8000 --name quiz-container quiz-app
   ```

4. **访问应用**
   - 学生端：http://localhost:8000
   - 管理后台：http://localhost:8000/admin

#### Docker管理命令

```bash
# 查看运行状态
docker ps

# 查看日志
docker logs quiz-container

# 停止容器
docker stop quiz-container

# 重启容器
docker restart quiz-container

# 删除容器
docker rm quiz-container

# 删除镜像
docker rmi quiz-app
```

### 方式二：本地开发部署

#### 前置要求
- Python 3.10+
- Node.js 16+
- npm 或 yarn

#### 后端部署

1. **进入后端目录**
   ```bash
   cd backend
   ```

2. **安装Python依赖**
   ```bash
   #python -m venv venv 
   # .\venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **启动后端服务**
   ```bash
   uvicorn main:app --host 0.0.0.0 --port 8000 --reload
   ```

#### 前端部署

1. **进入前端目录**
   ```bash
   cd frontend
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **构建生产版本**
   ```bash
   npm run build
   ```

## 使用说明

### 学生使用流程

1. **访问首页** - 打开 http://localhost:8000
2. **输入信息** - 填写姓名并选择班级（1-17班）
3. **开始答题** - 点击"开始答题"按钮进入答题页面
4. **完成答题** - 选择每道题的答案，点击"提交答案"
5. **查看结果** - 查看得分、正确答案和知识点解析

### 教师管理流程

1. **访问后台** - 打开 http://localhost:8000/admin
2. **登录认证** - 输入用户名 `admin` 和密码 `admin123`
3. **选择班级** - 在下拉框中选择要管理的班级
4. **导入题目** - 在文本框中粘贴JSON格式的题目数据
5. **提交更新** - 点击提交按钮更新题库
6. **查看成绩** - 在下方表格查看当天该班级的学生成绩

### 题目JSON格式示例

```json
[
  {
    "id": 1,
    "question": "以下哪个是常用的即时通讯软件？",
    "options": {
      "A": "QQ",
      "B": "Word",
      "C": "Excel",
      "D": "PowerPoint"
    },
    "answer": "A",
    "knowledge_point": "即时通讯软件是用于实时交流的应用程序，QQ是腾讯公司开发的即时通讯软件。"
  }
]
```

## API接口文档

### 学生端接口

- `GET /api/questions` - 获取所有题目
- `POST /api/submit` - 提交答案
- `GET /api/scores/{class_id}` - 获取班级成绩

### 管理端接口

- `GET /admin` - 管理后台页面
- `POST /admin/update-questions` - 更新题目

## 计分规则

- **总分**：100分
- **单题分值**：100 ÷ 题目总数
- **最终得分**：答对题目数量 × 单题分值（四舍五入取整）

## 数据库设计

### Questions表（题目表）
- `id` - 主键
- `question_content` - 题目内容
- `option_a, option_b, option_c, option_d` - 选项
- `answer` - 正确答案
- `knowledge_point` - 知识点解析

### Submissions表（提交记录表）
- `id` - 主键
- `student_name` - 学生姓名
- `score` - 得分
- `submission_date` - 提交日期
- `class_id` - 班级号

### SubmissionDetails表（答题详情表）
- `id` - 主键
- `submission_id` - 提交记录ID（外键）
- `question_id` - 题目ID（外键）
- `selected_answer` - 学生选择的答案

## 故障排除

### 常见问题

1. **Docker构建失败**
   - 检查Docker是否正常运行
   - 确保网络连接正常，能够下载依赖

2. **端口占用**
   - 使用 `docker run -p 8080:8000 quiz-app` 更换端口
   - 或停止占用8000端口的其他服务

3. **数据库连接错误**
   - 确保SQLite数据库文件有读写权限
   - 检查数据库文件路径是否正确

4. **前端页面无法访问**
   - 确认后端服务正常运行
   - 检查CORS配置是否正确

### 日志查看

```bash
# 查看Docker容器日志
docker logs quiz-container

# 实时查看日志
docker logs -f quiz-container
```

## 开发说明

### 开发环境设置

1. 克隆项目到本地
2. 按照"本地开发部署"步骤设置环境
3. 后端默认运行在 http://localhost:8000
4. 前端开发服务器运行在 http://localhost:3000

### 代码规范

- 后端遵循PEP 8 Python编码规范
- 前端使用Vue 3 Composition API
- 提交代码前请确保通过所有测试

## 许可证

本项目仅供教育用途使用。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues：在GitHub仓库中提交Issue
- 邮箱：[联系邮箱]

---

**注意**：首次运行时，系统会自动创建数据库表结构。管理员需要先通过后台导入题目，学生才能进行答题。