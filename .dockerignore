# Git
.git
.gitignore

# Documentation
README.md
*.md

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Database
*.db
*.sqlite
*.sqlite3
data/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Frontend build artifacts
frontend/node_modules/
frontend/dist/
frontend/.next/
frontend/out/

# Backend cache
backend/__pycache__/
backend/*.pyc

# Testing
.pytest_cache/
.coverage
htmlcov/

# Temporary files
*.tmp
*.temp

# Docker
Dockerfile*
docker-compose*
.dockerignore

# CI/CD
.github/
.gitlab-ci.yml
.travis.yml

# Backup files
*.bak
*.backup