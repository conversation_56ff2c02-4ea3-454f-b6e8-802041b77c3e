高中信息技术选择题练习平台 v6.0 - 打包部署指南

========================================
📦 最简单的打包方法
========================================

方案一：Docker打包（推荐）
--------------------------
1. 确保目标电脑已安装Docker Desktop
   - 如未安装，运行：docker-install.bat

2. 将整个项目文件夹复制到目标电脑

3. 在目标电脑上运行：deploy-package.bat

4. 访问：http://localhost:8000


方案二：便携式打包
------------------
1. 将整个项目文件夹复制到目标电脑

2. 在目标电脑上运行：portable-deploy.bat
   （会自动下载安装Python和Node.js）

3. 访问：http://localhost:8000


========================================
📁 需要复制的文件
========================================

必需文件：
- backend/ 文件夹（后端代码）
- frontend/ 文件夹（前端代码）
- Dockerfile
- docker-compose.yml
- deploy-package.bat
- portable-deploy.bat
- quick-start.bat
- stop-service.bat
- docker-install.bat

可选文件：
- README.md
- DEPLOYMENT-GUIDE.md
- .env.example


========================================
🚀 快速部署步骤
========================================

1. 压缩整个项目文件夹
2. 发送给目标用户
3. 用户解压到任意目录
4. 双击运行 deploy-package.bat（推荐）
   或 portable-deploy.bat（备选）
5. 等待自动部署完成
6. 浏览器自动打开应用


========================================
⚠️ 注意事项
========================================

- 目标电脑需要Windows 10/11 64位
- 确保8000端口未被占用
- 首次运行需要网络连接（下载依赖）
- Docker方式需要管理员权限安装Docker
- 便携式方式会自动安装Python和Node.js


========================================
🔧 故障排除
========================================

问题1：Docker未安装
解决：运行 docker-install.bat 获取安装指导

问题2：端口被占用
解决：修改 docker-compose.yml 中的端口号

问题3：防火墙阻止
解决：允许Python.exe通过防火墙

问题4：权限不足
解决：右键"以管理员身份运行"


========================================
📞 技术支持
========================================

如遇问题，请提供：
- 操作系统版本
- 错误信息截图
- 使用的部署方式
- 日志文件内容