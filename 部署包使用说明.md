# 🎯 高中信息技术选择题练习平台 - 部署包使用说明

## 📦 打包完成！

恭喜！你的项目已经成功创建了完整的部署包。现在你可以轻松地在任何Windows 11电脑上运行这个项目。

## 🚀 最简单的使用方法

### 步骤1：准备部署包
将以下文件和文件夹打包发送给目标用户：

**必需文件：**
- `backend/` 文件夹
- `frontend/` 文件夹  
- `Dockerfile`
- `docker-compose.yml`
- `deploy-package.bat` ⭐（推荐方式）
- `portable-deploy.bat` ⭐（备选方式）
- `quick-start.bat`
- `stop-service.bat`
- `docker-install.bat`

**说明文档：**
- `打包说明.txt`
- `DEPLOYMENT-GUIDE.md`
- `部署包使用说明.md`

### 步骤2：在目标电脑上部署

#### 方式一：Docker部署（推荐）
```
1. 解压部署包到任意目录
2. 双击运行 deploy-package.bat
3. 如果提示Docker未安装，运行 docker-install.bat
4. 等待自动部署完成
5. 浏览器自动打开 http://localhost:8000
```

#### 方式二：便携式部署（备选）
```
1. 解压部署包到任意目录
2. 双击运行 portable-deploy.bat
3. 脚本会自动下载安装Python和Node.js
4. 等待部署完成
5. 浏览器自动打开 http://localhost:8000
```

## 📋 系统要求

- **操作系统**：Windows 10/11 64位
- **内存**：至少2GB可用内存
- **磁盘**：至少1GB可用空间
- **网络**：首次部署需要网络连接
- **权限**：可能需要管理员权限（安装依赖时）

## 🎮 使用指南

### 访问地址
- **学生答题页面**：http://localhost:8000
- **教师管理后台**：http://localhost:8000/admin

### 默认账号
- **管理员账号**：admin
- **管理员密码**：admin123

⚠️ **重要**：首次登录后请立即修改默认密码！

### 功能特性
- ✅ 智能选择题练习系统
- ✅ 实时成绩统计分析  
- ✅ 教师后台管理
- ✅ 响应式设计，支持手机访问
- ✅ 安全的用户认证
- ✅ 详细的学习报告

## 🔧 管理命令

### 启动服务
```batch
# Docker方式
deploy-package.bat

# 便携式方式（首次）
portable-deploy.bat

# 便携式方式（后续）
quick-start.bat
```

### 停止服务
```batch
# Docker方式
stop-service.bat

# 便携式方式
关闭命令行窗口
```

### 查看日志
```batch
# Docker方式
docker-compose logs -f

# 便携式方式
查看命令行窗口输出
```

## ⚠️ 常见问题

### 问题1：端口被占用
**现象**：提示8000端口已被使用
**解决**：
1. 关闭占用8000端口的程序
2. 或修改`docker-compose.yml`中的端口映射

### 问题2：Docker未启动
**现象**：Docker相关错误
**解决**：
1. 启动Docker Desktop
2. 等待状态变为"Running"
3. 重新运行部署脚本

### 问题3：防火墙阻止
**现象**：无法访问网页
**解决**：
1. 允许Python.exe通过Windows防火墙
2. 或临时关闭防火墙测试

### 问题4：权限不足
**现象**：安装失败或访问被拒绝
**解决**：
1. 右键部署脚本
2. 选择"以管理员身份运行"

## 📊 性能参考

| 配置 | 并发用户 | 内存使用 | 磁盘使用 |
|------|----------|----------|----------|
| 最小配置 | 20-30人 | 1-2GB | 500MB |
| 推荐配置 | 50-100人 | 2-4GB | 1-2GB |
| 高性能配置 | 100+人 | 4-8GB | 2-5GB |

## 🔄 版本更新

当有新版本时：
1. 备份`data`文件夹（保存题库数据）
2. 停止当前服务
3. 替换所有文件（保留data文件夹）
4. 重新运行部署脚本

## 📞 技术支持

如果遇到问题，请提供以下信息：
- Windows版本
- 使用的部署方式
- 错误信息截图
- 日志文件内容

---

## 🎉 恭喜！

你现在拥有了一个完整的、可在任何Windows 11电脑上运行的高中信息技术选择题练习平台！

**特点：**
- ✅ 一键部署，无需复杂配置
- ✅ 自动安装所有依赖
- ✅ 完整的用户界面和管理功能
- ✅ 支持多用户同时使用
- ✅ 数据持久化保存

**适用场景：**
- 🏫 学校机房部署
- 💻 教师个人电脑
- 🏠 学生家庭练习
- 📱 移动设备访问

祝你使用愉快！🚀