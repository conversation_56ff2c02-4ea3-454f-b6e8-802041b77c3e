<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <rect width="1920" height="1080" fill="#f8fafc"/>
  <rect x="0" y="0" width="1920" height="80" fill="#7c3aed"/>
  <text x="40" y="50" font-family="Arial" font-size="26" fill="#fff" font-weight="700">错题解析</text>

  <rect x="60" y="120" width="1800" height="900" fill="#fff" stroke="#e5e7eb" rx="14"/>

  <text x="100" y="190" font-family="Arial" font-size="18" fill="#0f172a" font-weight="700">题目</text>
  <rect x="100" y="210" width="1720" height="130" fill="#f1f5f9" stroke="#e2e8f0" rx="10"/>
  <text x="120" y="250" font-family="Arial" font-size="16" fill="#111827">以下代码运行后变量y的值为多少？</text>

  <!-- 选项与正确/错误标记 -->
  <rect x="100" y="360" width="1720" height="60" fill="#ecfdf5" stroke="#10b981" rx="10"/>
  <text x="120" y="398" font-family="Arial" font-size="18" fill="#065f46">正确答案：B. 42</text>

  <rect x="100" y="440" width="1720" height="60" fill="#fef2f2" stroke="#fecaca" rx="10"/>
  <text x="120" y="478" font-family="Arial" font-size="18" fill="#b91c1c">你的选择：A. 40</text>

  <!-- 解析区 -->
  <text x="100" y="540" font-family="Arial" font-size="18" fill="#0f172a" font-weight="700">解析</text>
  <rect x="100" y="560" width="1720" height="420" fill="#f9fafb" stroke="#e5e7eb" rx="10"/>
  <text x="120" y="600" font-family="Arial" font-size="16" fill="#334155">1. 题目考查变量赋值与运算顺序</text>
  <text x="120" y="640" font-family="Arial" font-size="16" fill="#334155">2. 正确计算路径为：先乘法→再加法→最后赋值</text>
  <text x="120" y="680" font-family="Arial" font-size="16" fill="#334155">3. 可通过举例验证结果</text>
</svg>