#!/usr/bin/env python3
import sqlite3
import json

def check_llm_config():
    # 连接数据库
    conn = sqlite3.connect('backend/questions.db')
    cursor = conn.cursor()
    
    print("=== 检查大语言模型配置 ===")
    cursor.execute("""
        SELECT id, config_name, api_key, base_url, model_name, is_active, created_time, updated_time
        FROM llm_configs 
        ORDER BY id
    """)
    
    configs = cursor.fetchall()
    if not configs:
        print("数据库中没有LLM配置")
    else:
        for config in configs:
            status = "激活" if config[5] == 1 else "未激活"
            # 隐藏API密钥的大部分内容
            masked_key = config[2][:8] + "..." + config[2][-4:] if len(config[2]) > 12 else "***"
            print(f"ID: {config[0]}")
            print(f"  配置名称: {config[1]}")
            print(f"  API密钥: {masked_key}")
            print(f"  Base URL: {config[3]}")
            print(f"  模型名称: {config[4]}")
            print(f"  状态: {status}")
            print(f"  创建时间: {config[6]}")
            print(f"  更新时间: {config[7]}")
            print("-" * 50)
    
    conn.close()

if __name__ == "__main__":
    check_llm_config()
