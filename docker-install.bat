@echo off
chcp 65001 >nul
echo ========================================
echo    Docker Desktop 安装引导
echo ========================================
echo.

echo 🔍 正在检查系统环境...

:: 检查Windows版本
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo Windows版本：%VERSION%

:: 检查是否为Windows 10/11
if "%VERSION%" geq "10.0" (
    echo ✅ 系统版本支持Docker Desktop
) else (
    echo ❌ 系统版本过低，需要Windows 10或更高版本
    pause
    exit /b 1
)

echo.
echo 📥 Docker Desktop 安装步骤：
echo.
echo 1. 访问官方下载页面：
echo    https://www.docker.com/products/docker-desktop/
echo.
echo 2. 下载 "Docker Desktop for Windows"
echo.
echo 3. 运行安装程序，使用默认设置
echo.
echo 4. 安装完成后重启电脑
echo.
echo 5. 启动Docker Desktop，等待初始化完成
echo.
echo 6. 再次运行 deploy-package.bat 部署应用
echo.
echo ⚠️  注意事项：
echo    - 确保启用了Windows的虚拟化功能
echo    - 如果是Windows 10 Home版，需要先安装WSL2
echo.
echo 按任意键打开下载页面...
pause >nul
start https://www.docker.com/products/docker-desktop/