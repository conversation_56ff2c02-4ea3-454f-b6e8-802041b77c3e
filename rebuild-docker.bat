@echo off
echo === 重新构建并运行Docker容器 ===

echo 停止并删除现有容器...
docker stop unitechoose8 2>nul
docker rm unitechoose8 2>nul

echo 构建本地Docker镜像...
docker build -t unitechoose-local:latest .

if %errorlevel% equ 0 (
    echo ✅ 镜像构建成功
    
    echo 创建数据目录...
    if not exist "D:\docker\unitechoose\data" mkdir "D:\docker\unitechoose\data"
    
    echo 启动容器...
    docker run -d ^
        --name unitechoose8 ^
        -p 7605:8000 ^
        -v D:\docker\unitechoose\data:/app/data ^
        --network=mynet ^
        --restart unless-stopped ^
        unitechoose-local:latest
    
    if %errorlevel% equ 0 (
        echo ✅ 容器启动成功
        echo 等待5秒后查看日志...
        timeout /t 5 /nobreak >nul
        docker logs unitechoose8
        echo.
        echo 持续查看日志请运行: docker logs -f unitechoose8
        echo 访问地址: http://localhost:7605
    ) else (
        echo ❌ 容器启动失败
        pause
    )
) else (
    echo ❌ 镜像构建失败
    pause
)

pause
