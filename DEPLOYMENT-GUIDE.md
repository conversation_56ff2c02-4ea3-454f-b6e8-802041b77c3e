# 高中信息技术选择题练习平台 - 部署指南

## 📋 概述

本指南提供了在Windows 11系统上部署"高中信息技术选择题练习平台"的完整方案。我们提供了多种部署方式，适合不同的使用场景和技术水平。

## 🎯 部署方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **Docker部署** | 环境隔离、一键部署、跨平台 | 需要安装Docker | 推荐方案，适合所有用户 |
| **便携式部署** | 无需Docker、自动安装依赖 | 可能与系统环境冲突 | Docker无法使用时的备选 |
| **手动部署** | 完全控制、学习价值高 | 步骤复杂、容易出错 | 开发者或高级用户 |

## 🚀 方案一：Docker部署（推荐）

### 系统要求
- Windows 10/11 64位
- 至少4GB内存
- 至少2GB可用磁盘空间
- 管理员权限（首次安装Docker时需要）

### 部署步骤

#### 1. 安装Docker Desktop

如果未安装Docker，运行：
```batch
docker-install.bat
```

或手动安装：
1. 访问 https://www.docker.com/products/docker-desktop/
2. 下载Docker Desktop for Windows
3. 运行安装程序，使用默认设置
4. 重启电脑
5. 启动Docker Desktop，等待初始化完成

#### 2. 一键部署

```batch
deploy-package.bat
```

脚本会自动：
- ✅ 检查Docker环境
- 🔧 构建应用镜像
- 🚀 启动服务容器
- 🌐 打开浏览器访问应用

#### 3. 访问应用

- **主页面**：http://localhost:8000
- **管理后台**：http://localhost:8000/admin

#### 4. 管理服务

```batch
# 停止服务
stop-service.bat

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart
```

### 故障排除

**问题1：Docker未启动**
```
解决：启动Docker Desktop，等待状态变为"Running"
```

**问题2：端口被占用**
```
解决：修改docker-compose.yml中的端口映射
ports:
  - "8001:8000"  # 改为8001端口
```

**问题3：构建失败**
```
解决：清理Docker缓存
docker system prune -a
```

## 🔧 方案二：便携式部署

### 系统要求
- Windows 10/11 64位
- 至少2GB内存
- 至少1GB可用磁盘空间
- 网络连接（首次安装依赖时）

### 部署步骤

#### 1. 首次部署

```batch
portable-deploy.bat
```

脚本会自动：
- 📥 下载并安装Python 3.10
- 📥 下载并安装Node.js 18
- 🔧 创建Python虚拟环境
- 📦 安装所有依赖包
- 🏗️ 构建前端静态文件
- 🚀 启动应用服务

#### 2. 后续启动

```batch
quick-start.bat
```

#### 3. 访问应用

- **主页面**：http://localhost:8000
- **管理后台**：http://localhost:8000/admin

### 故障排除

**问题1：Python安装失败**
```
解决：手动安装Python 3.10+
下载地址：https://www.python.org/downloads/
安装时勾选"Add Python to PATH"
```

**问题2：Node.js安装失败**
```
解决：手动安装Node.js 18+
下载地址：https://nodejs.org/
```

**问题3：依赖安装失败**
```
解决：检查网络连接，或使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 📦 创建分发包

### 生成部署包

```batch
create-package.bat
```

这会创建一个完整的部署包，包含：
- 📁 所有源代码文件
- 🔧 部署脚本
- 📝 说明文档
- 🐳 Docker配置文件

### 分发到其他电脑

1. 将生成的 `unitechoose-v6.0-windows` 文件夹复制到目标电脑
2. 在目标电脑上运行对应的部署脚本
3. 首次推荐使用Docker方式部署

## 🛠️ 手动部署（高级用户）

### 环境准备

1. **安装Python 3.10+**
   ```batch
   python --version
   ```

2. **安装Node.js 18+**
   ```batch
   node --version
   npm --version
   ```

### 后端部署

1. **创建虚拟环境**
   ```batch
   cd backend
   python -m venv venv
   venv\Scripts\activate
   ```

2. **安装依赖**
   ```batch
   pip install -r requirements.txt
   ```

3. **启动后端**
   ```batch
   python main.py
   ```

### 前端部署

1. **安装依赖**
   ```batch
   cd frontend
   npm install
   ```

2. **构建前端**
   ```batch
   npm run build
   ```

3. **复制静态文件**
   ```batch
   xcopy /E /Y dist\* ..\backend\static\
   ```

## 🔐 安全配置

### 生产环境建议

1. **修改默认密码**
   - 管理员账号：admin/admin123
   - 建议首次登录后立即修改

2. **配置HTTPS**
   ```yaml
   # docker-compose.yml
   environment:
     - SSL_CERT_PATH=/app/certs/cert.pem
     - SSL_KEY_PATH=/app/certs/key.pem
   ```

3. **数据库备份**
   ```batch
   # 备份数据库
   copy data\questions.db data\questions_backup.db
   ```

## 📊 性能优化

### 系统资源

- **最小配置**：2GB内存，1GB磁盘
- **推荐配置**：4GB内存，5GB磁盘
- **高并发配置**：8GB内存，10GB磁盘

### 并发用户数

- **Docker部署**：支持50-100并发用户
- **便携式部署**：支持20-50并发用户
- **集群部署**：支持500+并发用户

## 🆘 技术支持

### 常见问题

1. **端口冲突**
   - 检查8000端口是否被占用
   - 修改配置文件中的端口设置

2. **防火墙问题**
   - 允许Python.exe通过防火墙
   - 开放8000端口的入站规则

3. **权限问题**
   - 以管理员身份运行部署脚本
   - 确保对项目目录有写入权限

### 日志查看

```batch
# Docker方式
docker-compose logs -f

# 便携式方式
查看命令行窗口输出
```

### 联系支持

如遇到无法解决的问题，请提供：
- 操作系统版本
- 错误信息截图
- 部署方式
- 日志文件内容

## 📈 版本更新

### 更新步骤

1. **备份数据**
   ```batch
   copy data\questions.db data\questions_backup.db
   ```

2. **停止服务**
   ```batch
   stop-service.bat
   ```

3. **替换文件**
   - 保留data目录
   - 替换其他所有文件

4. **重新部署**
   ```batch
   deploy-package.bat
   ```

### 版本历史

- **v6.0**：修复后台管理问题，优化用户体验
- **v5.x**：添加题库管理功能
- **v4.x**：实现用户认证系统
- **v3.x**：基础答题功能

---

## 📝 总结

本部署指南提供了完整的部署方案，推荐按以下优先级选择：

1. **首选**：Docker部署 - 最稳定可靠
2. **备选**：便携式部署 - 简单快速
3. **高级**：手动部署 - 完全控制

无论选择哪种方案，都能快速在Windows 11系统上运行本项目。如有问题，请参考故障排除部分或寻求技术支持。