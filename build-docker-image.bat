@echo off
chcp 65001 >nul
echo ========================================
echo    Docker镜像构建和部署脚本
echo ========================================
echo.

set IMAGE_NAME=liured88/unitechoose9
set CONTAINER_NAME=unitechoose9
set PORT=7605
set DATA_DIR=/bak/docker/unitechoose/data

echo [1/6] 停止并删除现有容器...
docker stop %CONTAINER_NAME% 2>nul
docker rm %CONTAINER_NAME% 2>nul
echo ✅ 现有容器已清理

echo.
echo [2/6] 检查必需文件...
if not exist "backend\main.py" (
    echo ❌ 错误: backend\main.py 文件不存在
    echo 请确保在项目根目录执行此脚本
    pause
    exit /b 1
)

if not exist "frontend\package.json" (
    echo ❌ 错误: frontend\package.json 文件不存在
    echo 请确保在项目根目录执行此脚本
    pause
    exit /b 1
)

if not exist "sample_questions.json" (
    echo ❌ 错误: sample_questions.json 文件不存在
    echo 请确保在项目根目录执行此脚本
    pause
    exit /b 1
)

echo ✅ 必需文件检查通过

echo.
echo [3/6] 使用优化的Dockerfile构建镜像...
echo 镜像名称: %IMAGE_NAME%
echo 构建开始时间: %date% %time%

docker build -f Dockerfile.optimized -t %IMAGE_NAME% .

if %errorlevel% neq 0 (
    echo ❌ 镜像构建失败
    echo 请检查构建日志中的错误信息
    pause
    exit /b 1
)

echo ✅ 镜像构建成功
echo 构建完成时间: %date% %time%

echo.
echo [4/6] 检查镜像信息...
docker images %IMAGE_NAME%

echo.
echo [5/6] 启动容器...
echo 容器名称: %CONTAINER_NAME%
echo 端口映射: %PORT%:8000
echo 数据目录: %DATA_DIR%

docker run -d ^
    --name %CONTAINER_NAME% ^
    -p %PORT%:8000 ^
    -v %DATA_DIR%:/app/data ^
    --network=mynet ^
    --restart unless-stopped ^
    %IMAGE_NAME%

if %errorlevel% neq 0 (
    echo ❌ 容器启动失败
    pause
    exit /b 1
)

echo ✅ 容器启动成功

echo.
echo [6/6] 检查容器状态...
timeout /t 3 /nobreak >nul
docker ps | findstr %CONTAINER_NAME%

echo.
echo ========================================
echo           部署完成！
echo ========================================
echo 容器名称: %CONTAINER_NAME%
echo 访问地址: http://localhost:%PORT%
echo 数据目录: %DATA_DIR%
echo.
echo 常用命令:
echo   查看日志: docker logs -f %CONTAINER_NAME%
echo   停止容器: docker stop %CONTAINER_NAME%
echo   重启容器: docker restart %CONTAINER_NAME%
echo   进入容器: docker exec -it %CONTAINER_NAME% bash
echo ========================================

echo.
echo 等待5秒后显示容器日志...
timeout /t 5 /nobreak >nul
echo.
echo === 容器启动日志 ===
docker logs %CONTAINER_NAME%

echo.
echo 按任意键退出...
pause >nul
