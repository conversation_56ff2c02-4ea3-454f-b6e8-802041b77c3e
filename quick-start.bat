@echo off
chcp 65001 >nul
echo ========================================
echo    高中信息技术选择题练习平台
echo    快速启动脚本 v6.0
echo ========================================
echo.

set "PROJECT_DIR=%~dp0"
set "VENV_DIR=%PROJECT_DIR%venv"

:: 检查虚拟环境
if not exist "%VENV_DIR%" (
    echo ❌ 虚拟环境不存在，请先运行 portable-deploy.bat
    pause
    exit /b 1
)

echo ✅ 检测到已配置的环境
echo.

:: 激活虚拟环境并启动
echo 🚀 启动应用...
cd /d "%PROJECT_DIR%backend"

:: 启动后端服务
start "后端服务" cmd /k "call "%VENV_DIR%\Scripts\activate.bat" && python main.py"

:: 等待服务启动
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo ✅ 应用启动完成！
echo.
echo 🌐 访问地址：http://localhost:8000
echo 📊 管理后台：http://localhost:8000/admin
echo ========================================
echo.

echo 按任意键打开浏览器访问应用...
pause >nul
start http://localhost:8000