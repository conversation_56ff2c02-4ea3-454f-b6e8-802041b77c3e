#!/usr/bin/env python3
import requests
import json

# 测试提交答案API
def test_submit_answers():
    url = "http://localhost:8001/api/submit"
    
    # 测试数据：学生回答所有10道题目，部分答错
    test_data = {
        "student_name": "测试学生2",
        "class_id": 1,
        "answers": [
            {"questionId": 1, "selectedAnswer": "A"},  # 正确
            {"questionId": 2, "selectedAnswer": "A"},  # 错误，正确答案是D
            {"questionId": 3, "selectedAnswer": "B"},  # 正确
            {"questionId": 4, "selectedAnswer": "A"},  # 错误，正确答案是B
            {"questionId": 5, "selectedAnswer": "C"},  # 正确
            {"questionId": 6, "selectedAnswer": "B"},  # 正确
            {"questionId": 7, "selectedAnswer": "A"},  # 错误，正确答案是D
            {"questionId": 8, "selectedAnswer": "B"},  # 正确
            {"questionId": 9, "selectedAnswer": "A"},  # 正确
            {"questionId": 10, "selectedAnswer": "C"}  # 正确
        ]
    }
    
    try:
        response = requests.post(url, json=test_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("提交成功！")
            print(f"得分: {result['score']}")
            print(f"题目数量: {len(result['full_results'])}")
            
            # 统计正确答案数量
            correct_count = sum(1 for r in result['full_results'] if r['isCorrect'])
            print(f"正确答案数: {correct_count}")
            print(f"错误答案数: {len(result['full_results']) - correct_count}")
            print(f"正确率: {correct_count / len(result['full_results']) * 100:.1f}%")
            
            print("\n详细结果:")
            for i, result_item in enumerate(result['full_results'], 1):
                status = "✓" if result_item['isCorrect'] else "✗"
                print(f"第{i}题: {status} 你的答案: {result_item['yourAnswer']} 正确答案: {result_item['correctAnswer']}")
                
        else:
            print(f"提交失败: {response.text}")
            
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_submit_answers()
