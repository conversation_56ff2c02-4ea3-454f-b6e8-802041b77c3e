import sqlite3
import os
from datetime import date

# 检查数据库文件是否存在
db_path = 'backend/questions.db'
print(f"数据库文件是否存在: {os.path.exists(db_path)}")

if os.path.exists(db_path):
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # 查看submissions表结构
    print("\n=== submissions表结构 ===")
    cursor.execute("PRAGMA table_info(submissions)")
    columns = cursor.fetchall()
    for column in columns:
        print(f"列{column[0]}: {column[1]} ({column[2]})")
    
    # 查看所有提交记录的详细信息
    print("\n=== 所有提交记录详细信息 ===")
    cursor.execute("SELECT id, student_name, score, submission_date, class_id, submission_time, client_ip FROM submissions")
    submissions = cursor.fetchall()
    if submissions:
        print("ID | 学生姓名 | 得分 | 提交日期 | 班级ID | 提交时间 | 客户端IP")
        print("-" * 80)
        for submission in submissions:
            print(f"{submission[0]} | {submission[1]} | {submission[2]} | {submission[3]} | {submission[4]} | {submission[5]} | {submission[6]}")
    else:
        print("没有提交记录")
    
    # 关闭连接
    conn.close()
else:
    print("数据库文件不存在")