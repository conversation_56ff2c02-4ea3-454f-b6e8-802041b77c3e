@echo off
chcp 65001 >nul
echo ========================================
echo      Docker构建文件检查工具
echo ========================================
echo.

set ERROR_COUNT=0

echo [检查] 项目根目录文件...

if exist "backend\main.py" (
    echo ✅ backend\main.py - 存在
) else (
    echo ❌ backend\main.py - 缺失
    set /a ERROR_COUNT+=1
)

if exist "backend\requirements.txt" (
    echo ✅ backend\requirements.txt - 存在
) else (
    echo ❌ backend\requirements.txt - 缺失
    set /a ERROR_COUNT+=1
)

if exist "frontend\package.json" (
    echo ✅ frontend\package.json - 存在
) else (
    echo ❌ frontend\package.json - 缺失
    set /a ERROR_COUNT+=1
)

if exist "frontend\src" (
    echo ✅ frontend\src - 存在
) else (
    echo ❌ frontend\src - 缺失
    set /a ERROR_COUNT+=1
)

if exist "sample_questions.json" (
    echo ✅ sample_questions.json - 存在
) else (
    echo ❌ sample_questions.json - 缺失
    set /a ERROR_COUNT+=1
)

if exist "Dockerfile.optimized" (
    echo ✅ Dockerfile.optimized - 存在
) else (
    echo ❌ Dockerfile.optimized - 缺失
    set /a ERROR_COUNT+=1
)

echo.
echo [检查] 可选文件...

if exist "docker-start.sh" (
    echo ✅ docker-start.sh - 存在 ^(可选^)
) else (
    echo ⚠️  docker-start.sh - 缺失 ^(可选，将使用内置脚本^)
)

if exist "docker-compose.yml" (
    echo ✅ docker-compose.yml - 存在 ^(可选^)
) else (
    echo ⚠️  docker-compose.yml - 缺失 ^(可选^)
)

echo.
echo ========================================

if %ERROR_COUNT% equ 0 (
    echo ✅ 所有必需文件都存在，可以开始构建！
    echo.
    echo 推荐的构建命令:
    echo   Windows: build-docker-image.bat
    echo   Linux:   ./build-docker-image.sh
    echo.
    echo 或者手动构建:
    echo   docker build -f Dockerfile.optimized -t liured88/unitechoose9 .
) else (
    echo ❌ 发现 %ERROR_COUNT% 个缺失文件，请先解决这些问题
    echo.
    echo 请确保:
    echo 1. 在项目根目录执行此脚本
    echo 2. 所有必需文件都存在
    echo 3. 文件路径正确
)

echo ========================================
echo.
pause
