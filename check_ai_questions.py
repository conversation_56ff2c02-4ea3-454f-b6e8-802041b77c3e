#!/usr/bin/env python3
import sqlite3

def check_ai_questions():
    # 连接数据库
    conn = sqlite3.connect('backend/questions.db')
    cursor = conn.cursor()
    
    print("=== 检查AI生成的题目 ===")
    
    # 查找AI生成的题库
    cursor.execute("""
        SELECT id, name, description, question_count, created_by
        FROM question_banks 
        WHERE created_by = 'AI'
        ORDER BY created_time DESC
    """)
    
    banks = cursor.fetchall()
    if not banks:
        print("没有找到AI生成的题库")
        return
    
    for bank in banks:
        print(f"题库ID: {bank[0]}")
        print(f"题库名称: {bank[1]}")
        print(f"题库描述: {bank[2]}")
        print(f"题目数量: {bank[3]}")
        print(f"创建者: {bank[4]}")
        print("-" * 50)
        
        # 查看该题库中的题目
        cursor.execute("""
            SELECT question_content, option_a, option_b, option_c, option_d, 
                   answer, knowledge_point, explanation
            FROM questions 
            WHERE question_bank_id = ?
            ORDER BY id
        """, (bank[0],))
        
        questions = cursor.fetchall()
        for i, q in enumerate(questions, 1):
            print(f"\n第{i}题:")
            print(f"  题目: {q[0]}")
            print(f"  A. {q[1]}")
            print(f"  B. {q[2]}")
            print(f"  C. {q[3]}")
            print(f"  D. {q[4]}")
            print(f"  正确答案: {q[5]}")
            print(f"  知识点: {q[6]}")
            print(f"  解析: {q[7]}")
        print("=" * 50)
    
    conn.close()

if __name__ == "__main__":
    check_ai_questions()
