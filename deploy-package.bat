@echo off
chcp 65001 >nul
echo ========================================
echo    高中信息技术选择题练习平台
echo    一键部署脚本 v6.0
echo ========================================
echo.

:: 检查Docker是否安装
echo [1/5] 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Docker，请先安装Docker Desktop
    echo 下载地址：https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)
echo ✅ Docker环境检查通过

:: 检查Docker是否运行
echo [2/5] 检查Docker服务状态...
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：Docker服务未启动，请启动Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker服务运行正常

:: 停止可能存在的旧容器
echo [3/5] 清理旧版本...
docker-compose down >nul 2>&1

:: 构建并启动应用
echo [4/5] 构建并启动应用...
docker-compose up --build -d
if %errorlevel% neq 0 (
    echo ❌ 部署失败，请检查错误信息
    pause
    exit /b 1
)

:: 等待服务启动
echo [5/5] 等待服务启动...
timeout /t 10 /nobreak >nul

:: 检查服务状态
docker-compose ps

echo.
echo ========================================
echo ✅ 部署完成！
echo.
echo 🌐 访问地址：http://localhost:8000
echo 📊 管理后台：http://localhost:8000/admin
echo.
echo 💡 使用说明：
echo    - 学生答题：直接访问主页面
echo    - 教师管理：访问 /admin 页面
echo.
echo 🔧 管理命令：
echo    - 查看日志：docker-compose logs -f
echo    - 停止服务：docker-compose down
echo    - 重启服务：docker-compose restart
echo ========================================
echo.
echo 按任意键打开浏览器访问应用...
pause >nul
start http://localhost:8000