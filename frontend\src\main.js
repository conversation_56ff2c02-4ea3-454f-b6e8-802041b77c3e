import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import axios from 'axios'
import App from './App.vue'
import Home from './components/Home.vue'
import Quiz from './components/Quiz.vue'
import Result from './components/Result.vue'
import ClassAnalysis from './components/ClassAnalysis.vue'
import TeacherDashboard from './components/TeacherDashboard.vue'

// 配置axios默认baseURL - 根据环境自动选择
if (import.meta.env.DEV) {
  // 开发环境：指向本地后端服务
  axios.defaults.baseURL = 'http://localhost:8000'
} else {
  // 生产环境：使用相对路径，与前端同域
  axios.defaults.baseURL = ''
}

// 路由配置
const routes = [
  { path: '/', component: Home },
  { path: '/quiz', component: Quiz },
  { path: '/result', component: Result },
  { path: '/analysis', component: ClassAnalysis },
  { path: '/teacher-dashboard', component: TeacherDashboard },
  // 管理后台重定向到后端
  { 
    path: '/admin', 
    beforeEnter() {
      if (import.meta.env.DEV) {
        window.location.href = 'http://localhost:8000/admin'
      } else {
        window.location.href = '/admin'
      }
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const app = createApp(App)
app.use(router)
app.mount('#app')