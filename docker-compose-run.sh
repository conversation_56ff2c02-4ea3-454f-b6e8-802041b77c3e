#!/bin/bash

# 使用docker-compose运行项目的脚本

echo "=== 使用Docker Compose运行项目 ==="

# 停止现有容器
echo "停止现有容器..."
docker stop unitechoose8 2>/dev/null || true
docker rm unitechoose8 2>/dev/null || true

# 创建数据目录
echo "创建数据目录..."
mkdir -p ./data
mkdir -p ./logs

# 使用docker-compose构建并启动
echo "使用Docker Compose启动服务..."
docker-compose up --build -d

if [ $? -eq 0 ]; then
    echo "✅ 服务启动成功"
    echo "服务状态："
    docker-compose ps
    echo ""
    echo "查看日志："
    docker-compose logs -f
else
    echo "❌ 服务启动失败"
    echo "查看错误日志："
    docker-compose logs
    exit 1
fi
