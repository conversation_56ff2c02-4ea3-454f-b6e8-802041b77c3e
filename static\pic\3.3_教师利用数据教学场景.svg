<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0" x2="0" y1="0" y2="1">
      <stop offset="0%" stop-color="#e0f2fe"/>
      <stop offset="100%" stop-color="#f0f9ff"/>
    </linearGradient>
  </defs>
  <rect width="1920" height="1080" fill="url(#bg)"/>

  <!-- 教室场景 -->
  <rect x="100" y="120" width="1720" height="840" fill="#ffffff" stroke="#dbeafe" rx="18"/>
  <rect x="140" y="160" width="900" height="520" fill="#0b1f43" rx="12"/>
  <text x="160" y="200" font-family="Arial" font-size="18" fill="#93c5fd">数据投屏 · 正答率趋势</text>
  <polyline points="180,620 260,560 340,540 420,520 500,500 580,520 660,480 740,490" fill="none" stroke="#60a5fa" stroke-width="6"/>
  <g fill="#60a5fa">
    <circle cx="180" cy="620" r="6"/>
    <circle cx="260" cy="560" r="6"/>
    <circle cx="340" cy="540" r="6"/>
    <circle cx="420" cy="520" r="6"/>
    <circle cx="500" cy="500" r="6"/>
    <circle cx="580" cy="520" r="6"/>
    <circle cx="660" cy="480" r="6"/>
    <circle cx="740" cy="490" r="6"/>
  </g>

  <!-- 教师与学生剪影 -->
  <path d="M1250 500 q80 -120 160 0 v200 h-160 z" fill="#0f172a" opacity="0.9"/>
  <circle cx="1350" cy="460" r="40" fill="#0f172a"/>
  <circle cx="1500" cy="730" r="40" fill="#0f172a"/>
  <rect x="1460" y="770" width="80" height="120" fill="#0f172a" rx="20"/>
  <circle cx="1580" cy="750" r="36" fill="#0f172a"/>
  <rect x="1548" y="786" width="64" height="104" fill="#0f172a" rx="18"/>

  <!-- 提示标签 -->
  <rect x="100" y="700" width="1720" height="220" fill="#f8fafc" stroke="#e2e8f0" rx="12"/>
  <text x="120" y="740" font-family="Arial" font-size="20" fill="#0f172a" font-weight="700">基于学情数据的靶向讲解</text>
  <text x="120" y="780" font-family="Arial" font-size="16" fill="#334155">• 根据共性错题，重点讲解易错知识点；根据个体差异，分层推进</text>
  <text x="120" y="820" font-family="Arial" font-size="16" fill="#334155">• 课堂内快速检验掌握度，形成“教学-测评-分析-调教”闭环</text>
</svg>