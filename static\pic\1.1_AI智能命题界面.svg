<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#f8fafc"/>
  
  <!-- 顶部导航栏 -->
  <rect x="0" y="0" width="1920" height="80" fill="#1e40af"/>
  <text x="40" y="50" font-family="Arial, sans-serif" font-size="24" fill="white" font-weight="bold">AI每节一练 - 智能命题系统</text>
  
  <!-- 主要内容区域 -->
  <rect x="60" y="120" width="1800" height="900" fill="white" stroke="#e5e7eb" stroke-width="2" rx="12"/>
  
  <!-- 左侧输入区域 -->
  <rect x="100" y="160" width="800" height="820" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="8"/>
  <text x="120" y="200" font-family="Arial, sans-serif" font-size="20" fill="#374151" font-weight="bold">知识点输入</text>
  
  <!-- 输入框 -->
  <rect x="120" y="220" width="760" height="200" fill="white" stroke="#d1d5db" stroke-width="1" rx="6"/>
  <text x="140" y="250" font-family="Arial, sans-serif" font-size="16" fill="#6b7280">请输入本节课的核心知识点：</text>
  <text x="140" y="280" font-family="Arial, sans-serif" font-size="14" fill="#374151">• Python中的条件语句if-elif-else</text>
  <text x="140" y="310" font-family="Arial, sans-serif" font-size="14" fill="#374151">• 逻辑运算符的使用</text>
  <text x="140" y="340" font-family="Arial, sans-serif" font-size="14" fill="#374151">• 嵌套条件语句的应用</text>
  
  <!-- 难度选择 -->
  <text x="120" y="470" font-family="Arial, sans-serif" font-size="18" fill="#374151" font-weight="bold">难度设置</text>
  <circle cx="150" cy="500" r="8" fill="#3b82f6"/>
  <text x="170" y="508" font-family="Arial, sans-serif" font-size="14" fill="#374151">基础 (30%)</text>
  <circle cx="280" cy="500" r="8" fill="#3b82f6"/>
  <text x="300" y="508" font-family="Arial, sans-serif" font-size="14" fill="#374151">中等 (50%)</text>
  <circle cx="410" cy="500" r="8" fill="#6b7280" stroke="#d1d5db" stroke-width="2"/>
  <text x="430" y="508" font-family="Arial, sans-serif" font-size="14" fill="#374151">困难 (20%)</text>
  
  <!-- 题目数量 -->
  <text x="120" y="560" font-family="Arial, sans-serif" font-size="18" fill="#374151" font-weight="bold">题目数量</text>
  <rect x="120" y="580" width="100" height="40" fill="white" stroke="#d1d5db" stroke-width="1" rx="4"/>
  <text x="155" y="605" font-family="Arial, sans-serif" font-size="16" fill="#374151">8</text>
  
  <!-- 生成按钮 -->
  <rect x="120" y="660" width="200" height="50" fill="#3b82f6" rx="8"/>
  <text x="200" y="690" font-family="Arial, sans-serif" font-size="16" fill="white" font-weight="bold" text-anchor="middle">🤖 AI智能生成</text>
  
  <!-- 右侧预览区域 -->
  <rect x="940" y="160" width="880" height="820" fill="#f9fafb" stroke="#d1d5db" stroke-width="1" rx="8"/>
  <text x="960" y="200" font-family="Arial, sans-serif" font-size="20" fill="#374151" font-weight="bold">生成预览</text>
  
  <!-- 题目预览 -->
  <rect x="960" y="220" width="840" height="120" fill="white" stroke="#e5e7eb" stroke-width="1" rx="6"/>
  <text x="980" y="250" font-family="Arial, sans-serif" font-size="16" fill="#374151" font-weight="bold">题目1：以下Python代码的输出结果是？</text>
  <text x="980" y="280" font-family="monospace" font-size="14" fill="#1f2937">x = 10</text>
  <text x="980" y="300" font-family="monospace" font-size="14" fill="#1f2937">if x > 5 and x < 15:</text>
  <text x="980" y="320" font-family="monospace" font-size="14" fill="#1f2937">    print("条件成立")</text>
  
  <rect x="960" y="360" width="840" height="100" fill="white" stroke="#e5e7eb" stroke-width="1" rx="6"/>
  <text x="980" y="390" font-family="Arial, sans-serif" font-size="16" fill="#374151" font-weight="bold">题目2：下列关于elif语句的说法正确的是？</text>
  <text x="980" y="420" font-family="Arial, sans-serif" font-size="14" fill="#374151">A. elif可以单独使用</text>
  <text x="980" y="440" font-family="Arial, sans-serif" font-size="14" fill="#374151">B. elif必须配合if使用</text>
  
  <!-- AI处理状态 -->
  <rect x="960" y="800" width="840" height="60" fill="#ecfdf5" stroke="#10b981" stroke-width="1" rx="6"/>
  <text x="980" y="825" font-family="Arial, sans-serif" font-size="14" fill="#059669">✅ AI分析完成</text>
  <text x="980" y="845" font-family="Arial, sans-serif" font-size="12" fill="#059669">已生成8道题目，覆盖所有知识点，难度分布合理</text>
</svg>