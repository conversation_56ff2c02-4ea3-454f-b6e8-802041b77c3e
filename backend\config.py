# 配置文件
import os
from typing import Dict, Any

class Config:
    """应用配置类"""
    
    # DeepSeek API 配置（通过环境变量注入，避免将密钥硬编码到仓库）
    # 必填：DEEPSEEK_API_KEY
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    # 可覆盖：DEEPSEEK_BASE_URL/DEEPSEEK_MODEL（提供默认值）
    # DeepSeek官方兼容OpenAI风格的/v1路径，此处默认带上/v1，避免路径错误
    DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")
    DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")
    
    # 数据库配置
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./questions.db")
    
    # 安全配置
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
    ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "admin")
    ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD", "admin123")
    ADMIN_TOKEN = os.getenv("ADMIN_TOKEN", "admin_secret_token_2024")
    
    # 速率限制配置
    RATE_LIMIT_REQUESTS = int(os.getenv("RATE_LIMIT_REQUESTS", "100"))
    RATE_LIMIT_WINDOW = int(os.getenv("RATE_LIMIT_WINDOW", "60"))  # 秒
    
    # 登录安全配置
    MAX_LOGIN_ATTEMPTS = int(os.getenv("MAX_LOGIN_ATTEMPTS", "5"))
    LOGIN_LOCKOUT_DURATION = int(os.getenv("LOGIN_LOCKOUT_DURATION", "900"))  # 15分钟
    
    # 缓存配置
    CACHE_TTL = int(os.getenv("CACHE_TTL", "300"))  # 5分钟
    CACHE_MAX_SIZE = int(os.getenv("CACHE_MAX_SIZE", "1000"))
    
    # 文件上传配置
    MAX_FILE_SIZE = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB
    ALLOWED_FILE_TYPES = os.getenv("ALLOWED_FILE_TYPES", "json,txt,csv").split(",")
    
    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "app.log")
    
    # CORS配置
    ALLOWED_ORIGINS = os.getenv("ALLOWED_ORIGINS", "*").split(",")
    
    # 生产环境标志
    PRODUCTION = os.getenv("PRODUCTION", "false").lower() == "true"
    
    @classmethod
    def get_security_config(cls) -> Dict[str, Any]:
        """获取安全配置"""
        return {
            "secret_key": cls.SECRET_KEY,
            "admin_username": cls.ADMIN_USERNAME,
            "admin_password": cls.ADMIN_PASSWORD,
            "admin_token": cls.ADMIN_TOKEN,
            "rate_limit_requests": cls.RATE_LIMIT_REQUESTS,
            "rate_limit_window": cls.RATE_LIMIT_WINDOW,
            "max_login_attempts": cls.MAX_LOGIN_ATTEMPTS,
            "login_lockout_duration": cls.LOGIN_LOCKOUT_DURATION,
            "production": cls.PRODUCTION
        }

# 创建配置实例
config = Config()

# 保持向后兼容性
DEEPSEEK_API_KEY = config.DEEPSEEK_API_KEY
DEEPSEEK_BASE_URL = config.DEEPSEEK_BASE_URL
DEEPSEEK_MODEL = config.DEEPSEEK_MODEL

# 题目生成的系统提示词
QUESTION_GENERATION_SYSTEM_PROMPT = """
你是专业的选择题生成助手。根据用户输入生成高质量选择题JSON数据。

要求：
1. 每题4个选项（A、B、C、D），有唯一正确答案
2. 题目清晰准确，选项有适当迷惑性
3. 包含详细解析

输出JSON格式：
[
  {
    "question_content": "题目内容",
    "option_a": "选项A",
    "option_b": "选项B", 
    "option_c": "选项C",
    "option_d": "选项D",
    "answer": "正确答案(A/B/C/D)",
    "knowledge_point": "知识点",
    "explanation": "答案解析",
    "wrong_analysis": "错误选项分析",
    "difficulty_level": "难度(easy/medium/hard)",
    "topic_category": "题目分类"
  }
]

只返回JSON数据，不要其他文字。
"""