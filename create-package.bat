@echo off
chcp 65001 >nul
echo ========================================
echo    创建部署包脚本 v6.0
echo ========================================
echo.

set "PROJECT_DIR=%~dp0"
set "PACKAGE_NAME=unitechoose-v6.0-windows"
set "PACKAGE_DIR=%PROJECT_DIR%%PACKAGE_NAME%"

echo 📦 正在创建部署包：%PACKAGE_NAME%
echo.

:: 清理旧的打包目录
if exist "%PACKAGE_DIR%" (
    echo 🧹 清理旧的打包目录...
    rmdir /s /q "%PACKAGE_DIR%"
)

:: 创建打包目录结构
echo 📁 创建目录结构...
mkdir "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%\backend"
mkdir "%PACKAGE_DIR%\frontend"
mkdir "%PACKAGE_DIR%\data"
mkdir "%PACKAGE_DIR%\docs"

:: 复制后端文件
echo 📋 复制后端文件...
xcopy /E /Y "%PROJECT_DIR%backend\*" "%PACKAGE_DIR%\backend\" /EXCLUDE:"%PROJECT_DIR%\.gitignore"

:: 复制前端文件
echo 📋 复制前端文件...
xcopy /E /Y "%PROJECT_DIR%frontend\*" "%PACKAGE_DIR%\frontend\" /EXCLUDE:"%PROJECT_DIR%\.gitignore"

:: 复制Docker相关文件
echo 📋 复制Docker文件...
copy "%PROJECT_DIR%Dockerfile" "%PACKAGE_DIR%\"
copy "%PROJECT_DIR%docker-compose.yml" "%PACKAGE_DIR%\"
copy "%PROJECT_DIR%.dockerignore" "%PACKAGE_DIR%\"

:: 复制部署脚本
echo 📋 复制部署脚本...
copy "%PROJECT_DIR%deploy-package.bat" "%PACKAGE_DIR%\"
copy "%PROJECT_DIR%portable-deploy.bat" "%PACKAGE_DIR%\"
copy "%PROJECT_DIR%quick-start.bat" "%PACKAGE_DIR%\"
copy "%PROJECT_DIR%stop-service.bat" "%PACKAGE_DIR%\"
copy "%PROJECT_DIR%docker-install.bat" "%PACKAGE_DIR%\"

:: 复制其他必要文件
echo 📋 复制配置文件...
if exist "%PROJECT_DIR%README.md" copy "%PROJECT_DIR%README.md" "%PACKAGE_DIR%\"
if exist "%PROJECT_DIR%.env.example" copy "%PROJECT_DIR%.env.example" "%PACKAGE_DIR%\"

:: 创建部署说明文档
echo 📝 创建部署说明...
(
echo # 高中信息技术选择题练习平台 v6.0
echo.
echo ## 快速部署指南
echo.
echo ### 方案一：Docker部署（推荐）
echo.
echo 1. 确保已安装Docker Desktop
echo    - 如未安装，运行 `docker-install.bat` 获取安装指导
echo.
echo 2. 运行部署脚本
echo    ```
echo    deploy-package.bat
echo    ```
echo.
echo 3. 访问应用
echo    - 主页面：http://localhost:8000
echo    - 管理后台：http://localhost:8000/admin
echo.
echo ### 方案二：便携式部署
echo.
echo 1. 首次部署（自动安装Python和Node.js）
echo    ```
echo    portable-deploy.bat
echo    ```
echo.
echo 2. 后续启动（环境已配置）
echo    ```
echo    quick-start.bat
echo    ```
echo.
echo ### 停止服务
echo.
echo - Docker方式：运行 `stop-service.bat`
echo - 便携式方式：关闭命令行窗口
echo.
echo ## 系统要求
echo.
echo - Windows 10/11 64位
echo - 至少2GB可用内存
echo - 至少1GB可用磁盘空间
echo.
echo ## 功能特性
echo.
echo - 🎯 智能选择题练习系统
echo - 📊 实时成绩统计分析
echo - 👨‍🏫 教师后台管理
echo - 📱 响应式设计，支持移动端
echo - 🔒 安全的用户认证
echo - 📈 详细的学习报告
echo.
echo ## 技术支持
echo.
echo 如遇到问题，请检查：
echo 1. 防火墙是否阻止了8000端口
echo 2. 是否有其他程序占用8000端口
echo 3. Docker Desktop是否正常运行（Docker方式）
echo 4. Python和Node.js是否正确安装（便携式方式）
) > "%PACKAGE_DIR%\README.md"

:: 创建压缩包（如果有7zip）
echo 📦 创建压缩包...
where 7z >nul 2>&1
if %errorlevel% equ 0 (
    7z a -tzip "%PROJECT_DIR%%PACKAGE_NAME%.zip" "%PACKAGE_DIR%\*"
    echo ✅ 压缩包已创建：%PACKAGE_NAME%.zip
) else (
    echo ⚠️  未检测到7zip，跳过压缩步骤
    echo 💡 可手动压缩 %PACKAGE_NAME% 文件夹
)

echo.
echo ========================================
echo ✅ 部署包创建完成！
echo.
echo 📁 包目录：%PACKAGE_DIR%
echo 📦 包大小：
dir "%PACKAGE_DIR%" | find "个文件"
echo.
echo 🚀 分发说明：
echo 1. 将整个 %PACKAGE_NAME% 文件夹复制到目标电脑
echo 2. 在目标电脑上运行对应的部署脚本
echo 3. 首次推荐使用 Docker 方式部署
echo ========================================
echo.
pause