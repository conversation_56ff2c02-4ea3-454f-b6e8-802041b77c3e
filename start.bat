@echo off
chcp 65001 >nul
echo Starting Quiz Platform...
echo.
echo Starting backend service (port 8001)...
start "Backend Service" cmd /k "cd /d %~dp0 && call venv\Scripts\activate && uvicorn backend.main:app --host 0.0.0.0 --port 8001 --reload"
echo.
echo Waiting 3 seconds before starting frontend...
timeout /t 3 /nobreak >nul
echo.
echo Starting frontend service (port 3000)...
start "Frontend Service" cmd /k "cd /d %~dp0\frontend && npm run dev"
echo.
echo Services started successfully!
echo.
echo Frontend: http://localhost:3000
echo Backend API: http://localhost:8001
echo Admin Panel: http://localhost:8001/admin (username: admin, password: admin123)
echo.
echo Press any key to exit...
pause >nul