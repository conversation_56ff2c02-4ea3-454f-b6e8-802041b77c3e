#!/usr/bin/env python3
import sqlite3
import json

def check_database():
    # 连接数据库
    conn = sqlite3.connect('backend/questions.db')
    cursor = conn.cursor()
    
    print("=== 检查提交记录 ===")
    cursor.execute("""
        SELECT id, student_name, score, total_questions, correct_answers, accuracy_rate, submission_date
        FROM submissions 
        ORDER BY id DESC
        LIMIT 5
    """)
    
    submissions = cursor.fetchall()
    for sub in submissions:
        print(f"ID: {sub[0]}, 学生: {sub[1]}, 得分: {sub[2]}, 总题数: {sub[3]}, 正确数: {sub[4]}, 正确率: {sub[5]}%, 日期: {sub[6]}")
    
    print("\n=== 检查提交详情 ===")
    if submissions:
        latest_submission_id = submissions[0][0]
        cursor.execute("""
            SELECT sd.question_id, sd.selected_answer, sd.is_correct, q.answer as correct_answer
            FROM submission_details sd
            JOIN questions q ON sd.question_id = q.id
            WHERE sd.submission_id = ?
            ORDER BY sd.question_id
        """, (latest_submission_id,))
        
        details = cursor.fetchall()
        print(f"最新提交记录 (ID: {latest_submission_id}) 的详情:")
        for detail in details:
            status = "✓" if detail[2] == 1 else "✗"
            print(f"题目{detail[0]}: {status} 学生答案: {detail[1]}, 正确答案: {detail[3]}")
    
    conn.close()

if __name__ == "__main__":
    check_database()
