# Docker 容器化部署故障排除指南

## 🔧 常见问题及解决方案

### 1. "exec /app/start.sh: no such file or directory" 错误

**问题原因：**
- 使用了不完整的预构建镜像
- 构建时缺少启动脚本文件
- 文件路径不正确

**解决方案：**
```bash
# 方案1：使用优化的Dockerfile重新构建
docker build -f Dockerfile.optimized -t liured88/unitechoose9 .

# 方案2：使用自动化脚本
# Windows:
build-docker-image.bat

# Linux:
chmod +x build-docker-image.sh
./build-docker-image.sh
```

### 2. 构建失败 - 文件不存在

**检查必需文件：**
```bash
# Windows:
check-build-files.bat

# Linux:
ls -la backend/main.py frontend/package.json sample_questions.json
```

**必需文件列表：**
- `backend/main.py` - 后端主程序
- `backend/requirements.txt` - Python依赖
- `frontend/package.json` - 前端依赖配置
- `frontend/src/` - 前端源码目录
- `sample_questions.json` - 示例题目数据

### 3. 容器启动后立即退出

**检查容器日志：**
```bash
docker logs unitechoose9
```

**常见原因：**
- 端口冲突
- 数据目录权限问题
- 环境变量配置错误

**解决方案：**
```bash
# 检查端口占用
netstat -an | findstr :7605

# 创建数据目录
mkdir -p /bak/docker/unitechoose/data

# 重新启动容器
docker restart unitechoose9
```

### 4. 网络连接问题

**检查网络：**
```bash
# 检查网络是否存在
docker network ls | grep mynet

# 创建网络（如果不存在）
docker network create mynet

# 重新连接网络
docker network connect mynet unitechoose9
```

### 5. 数据持久化问题

**检查数据卷：**
```bash
# 检查数据目录
docker exec unitechoose9 ls -la /app/data

# 检查宿主机目录
ls -la /bak/docker/unitechoose/data
```

## 🚀 推荐的部署流程

### 步骤1：环境准备
```bash
# 确保在项目根目录
pwd
ls -la backend/ frontend/ sample_questions.json

# 检查Docker服务
docker --version
docker ps
```

### 步骤2：清理旧容器
```bash
docker stop unitechoose9 2>/dev/null || true
docker rm unitechoose9 2>/dev/null || true
```

### 步骤3：构建镜像
```bash
# 使用优化的Dockerfile
docker build -f Dockerfile.optimized -t liured88/unitechoose9 .

# 或使用自动化脚本
./build-docker-image.sh
```

### 步骤4：启动容器
```bash
docker run -d \
  --name unitechoose9 \
  -p 7605:8000 \
  -v /bak/docker/unitechoose/data:/app/data \
  --network=mynet \
  --restart unless-stopped \
  liured88/unitechoose9
```

### 步骤5：验证部署
```bash
# 检查容器状态
docker ps | grep unitechoose9

# 查看日志
docker logs unitechoose9

# 测试访问
curl http://localhost:7605/health
```

## 🔍 调试命令

### 容器调试
```bash
# 进入容器
docker exec -it unitechoose9 bash

# 检查进程
docker exec unitechoose9 ps aux

# 检查文件
docker exec unitechoose9 ls -la /app/

# 检查启动脚本
docker exec unitechoose9 cat /app/start.sh
```

### 日志分析
```bash
# 实时查看日志
docker logs -f unitechoose9

# 查看最近100行日志
docker logs --tail 100 unitechoose9

# 查看特定时间的日志
docker logs --since "2024-01-01T00:00:00" unitechoose9
```

### 性能监控
```bash
# 查看资源使用
docker stats unitechoose9

# 查看容器详细信息
docker inspect unitechoose9
```

## 📋 完整的重新部署命令

如果遇到任何问题，可以使用以下命令完全重新部署：

```bash
# 1. 停止并删除容器
docker stop unitechoose9 && docker rm unitechoose9

# 2. 删除旧镜像（可选）
docker rmi liured88/unitechoose9

# 3. 重新构建镜像
docker build -f Dockerfile.optimized -t liured88/unitechoose9 .

# 4. 创建数据目录
mkdir -p /bak/docker/unitechoose/data

# 5. 启动新容器
docker run -d \
  --name unitechoose9 \
  -p 7605:8000 \
  -v /bak/docker/unitechoose/data:/app/data \
  --network=mynet \
  --restart unless-stopped \
  liured88/unitechoose9

# 6. 验证部署
docker logs unitechoose9
```

## 📞 获取帮助

如果问题仍然存在，请提供以下信息：
1. 操作系统版本
2. Docker版本：`docker --version`
3. 错误日志：`docker logs unitechoose9`
4. 容器状态：`docker ps -a | grep unitechoose9`
5. 镜像信息：`docker images | grep unitechoose9`
