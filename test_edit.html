<!DOCTYPE html>
<html>
<head>
    <title>测试编辑功能</title>
</head>
<body>
    <h1>测试LLM配置编辑功能</h1>
    
    <div id="llmConfigForm" style="display: none; background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
        <h3 id="llmFormTitle">编辑配置</h3>
        <form>
            <input type="hidden" id="llmConfigId" value="">
            <label for="configName">配置名称:</label>
            <input type="text" id="configName" placeholder="例如：DeepSeek主配置、Gemini配置等" required><br><br>
            <label for="apiKey">API Key:</label>
            <input type="password" id="apiKey" placeholder="请输入API密钥" required><br><br>
            <label for="baseUrl">Base URL:</label>
            <input type="text" id="baseUrl" placeholder="例如：https://api.deepseek.com/v1" required><br><br>
            <label for="modelName">模型名称:</label>
            <input type="text" id="modelName" placeholder="例如：deepseek-chat" required><br><br>
            <label>
                <input type="checkbox" id="isActive"> 设为当前激活配置
            </label>
        </form>
    </div>
    
    <button onclick="testEdit()">测试编辑功能</button>
    
    <script>
        // 模拟配置数据
        let currentLLMConfigs = [
            {
                id: 1,
                config_name: "DeepSeek配置",
                api_key: "sk-test123",
                base_url: "https://api.deepseek.com/v1",
                model_name: "deepseek-chat",
                is_active: 1
            }
        ];
        
        function editLLMConfig(configId) {
            console.log('editLLMConfig called with configId:', configId);
            console.log('currentLLMConfigs:', currentLLMConfigs);
            
            const config = currentLLMConfigs.find(c => c.id === configId);
            console.log('Found config:', config);
            
            if (!config) {
                console.error('Config not found for id:', configId);
                return;
            }
            
            document.getElementById('llmFormTitle').textContent = '编辑配置';
            document.getElementById('llmConfigId').value = config.id;
            document.getElementById('configName').value = config.config_name;
            document.getElementById('apiKey').value = config.api_key;
            document.getElementById('baseUrl').value = config.base_url;
            document.getElementById('modelName').value = config.model_name;
            document.getElementById('isActive').checked = config.is_active === 1;
            document.getElementById('llmConfigForm').style.display = 'block';
            
            console.log('Form should be visible now');
        }
        
        function testEdit() {
            editLLMConfig(1);
        }
    </script>
</body>
</html>