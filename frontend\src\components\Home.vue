<template>
  <div class="home">
    <div class="container">
      <div class="welcome-header">
        <h1>🎓 高中信息技术选择题练习平台</h1>
        <p class="subtitle">欢迎来到在线练习系统，请填写信息开始答题</p>
      </div>
      
      <form @submit.prevent="startQuiz" class="start-form">
        <div class="form-group">
          <label for="studentName">👤 请输入你的姓名</label>
          <input 
            type="text" 
            id="studentName" 
            v-model="studentName" 
            placeholder="请输入你的真实姓名"
            required
          >
        </div>
        

        
        <button 
          type="submit" 
          class="btn start-btn"
          :disabled="!canStart"
        >
          🚀 开始答题
        </button>
      </form>
      
      <div class="navigation-section">
        <div class="nav-buttons">
          <button @click="$router.push('/analysis')" class="btn analysis-btn">
            📊 查看班级学情分析
          </button>
          <button @click="$router.push('/teacher')" class="btn teacher-btn">
            👨‍🏫 教师管理后台
          </button>
        </div>
      </div>
      
      <div class="info-section">
        <div class="info-card">
          <h3>📋 答题说明</h3>
          <ul>
            <li>请认真填写姓名信息</li>
            <li>每道题只能选择一个答案</li>
            <li>答题完成后可查看详细解析</li>
            <li>总分100分，祝你取得好成绩！</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      studentName: ''
    }
  },
  computed: {
    canStart() {
      return this.studentName.trim()
    }
  },
  methods: {
    startQuiz() {
      if (this.canStart) {
        // 将学生信息存储到sessionStorage
        sessionStorage.setItem('studentName', this.studentName.trim())
        // 默认设置为1班，因为题目已经绑定到特定班级
        sessionStorage.setItem('classId', '1')
        
        // 跳转到答题页面
        this.$router.push('/quiz')
      }
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.home::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  pointer-events: none;
}

.welcome-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
}

.welcome-header h1 {
  font-size: clamp(2rem, 5vw, 3rem);
  color: #fff;
  margin-bottom: 15px;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  font-weight: 700;
  letter-spacing: -0.5px;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: clamp(1rem, 3vw, 1.3rem);
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 30px;
  font-weight: 400;
  text-shadow: 0 2px 4px rgba(0,0,0,0.2);
  line-height: 1.6;
}

.start-form {
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}

.start-btn {
  width: 100%;
  font-size: 1.2em;
  padding: 18px;
  margin-top: 25px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
  position: relative;
  overflow: hidden;
}

.start-btn:hover {
  box-shadow: 0 12px 35px rgba(76, 175, 80, 0.5);
  background: linear-gradient(135deg, #45a049, #4CAF50);
}

.start-btn::after {
  content: '→';
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.3s ease;
}

.start-btn:hover::after {
  transform: translateY(-50%) translateX(5px);
}

.navigation-section {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}

.nav-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .nav-buttons {
    flex-direction: column;
    gap: 10px;
  }
}

.analysis-btn {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  font-size: 1.1em;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.analysis-btn:hover {
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
  transform: translateY(-2px);
}

.teacher-btn {
  background: linear-gradient(135deg, #9b59b6, #8e44ad);
  font-size: 1.1em;
  flex: 1;
  min-width: 200px;
  box-shadow: 0 8px 25px rgba(155, 89, 182, 0.3);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.teacher-btn:hover {
  box-shadow: 0 12px 35px rgba(155, 89, 182, 0.4);
  transform: translateY(-2px);
}

.info-section {
  margin-top: 50px;
  position: relative;
  z-index: 1;
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.info-card h3 {
  color: #333;
  margin-bottom: 20px;
  font-size: 1.4em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.info-card li {
  padding: 12px 0;
  border-bottom: 1px solid rgba(238, 238, 238, 0.5);
  color: #555;
  position: relative;
  padding-left: 25px;
  line-height: 1.6;
  transition: all 0.3s ease;
}

.info-card li::before {
  content: '✓';
  position: absolute;
  left: 0;
  top: 12px;
  color: #4CAF50;
  font-weight: bold;
  font-size: 14px;
}

.info-card li:hover {
  color: #333;
  transform: translateX(5px);
}

.info-card li:last-child {
  border-bottom: none;
}

@media (max-width: 768px) {
  .home {
    padding: 15px;
  }
  
  .welcome-header {
    margin-bottom: 30px;
  }
  
  .info-card {
    padding: 20px;
  }
  
  .start-btn {
    padding: 15px;
    font-size: 1.1em;
  }
  
  .nav-buttons .btn {
    min-width: auto;
    width: 100%;
  }
}
</style>