#!/usr/bin/env python3
import requests
import json

def test_ai_generate():
    url = "http://localhost:8001/admin/ai-generate"
    
    # 测试数据
    test_data = {
        "knowledge_point": "计算机网络基础知识",
        "count": 2,
        "difficulty": "easy",
        "topic": "网络协议"
    }
    
    try:
        print("=== 测试AI智能生成题目 ===")
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=test_data, timeout=30)
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AI生成成功！")
            print(f"生成题目数量: {result.get('generated_count', 0)}")
            print(f"题库名称: {result.get('bank_name', 'N/A')}")
            print(f"响应消息: {result.get('message', 'N/A')}")
            
            # 显示生成的题目
            if 'generated_questions' in result:
                print("\n生成的题目:")
                for i, q in enumerate(result['generated_questions'], 1):
                    print(f"\n第{i}题:")
                    print(f"  题目: {q.get('question_content', 'N/A')}")
                    print(f"  选项A: {q.get('option_a', 'N/A')}")
                    print(f"  选项B: {q.get('option_b', 'N/A')}")
                    print(f"  选项C: {q.get('option_c', 'N/A')}")
                    print(f"  选项D: {q.get('option_d', 'N/A')}")
                    print(f"  正确答案: {q.get('answer', 'N/A')}")
                    print(f"  知识点: {q.get('knowledge_point', 'N/A')}")
        else:
            print(f"❌ AI生成失败")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时，AI生成可能需要更长时间")
    except Exception as e:
        print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_ai_generate()
