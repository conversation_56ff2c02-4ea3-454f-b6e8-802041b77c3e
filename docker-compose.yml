version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=sqlite:///./data/questions.db
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - DEV_MODE=false
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - app-network

  # 开发模式配置
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
      - "3000:3000"
    environment:
      - DATABASE_URL=sqlite:///./data/questions.db
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - DEV_MODE=true
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backend:/app/backend
      - ./frontend:/app/frontend
    restart: unless-stopped
    profiles:
      - dev
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  app-data:
    driver: local
  app-logs:
    driver: local