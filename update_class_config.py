import sqlite3

# 连接数据库
conn = sqlite3.connect('backend/questions.db')
cursor = conn.cursor()

# 查看当前配置
cursor.execute('SELECT * FROM class_configs')
print('当前班级配置:', cursor.fetchall())

# 更新班级配置为1班
cursor.execute('UPDATE class_configs SET current_class_id = 1, class_name = "一班" WHERE id = 1')
conn.commit()

# 查看更新后的配置
cursor.execute('SELECT * FROM class_configs')
print('更新后班级配置:', cursor.fetchall())

conn.close()
print('班级配置已更新为1班！')