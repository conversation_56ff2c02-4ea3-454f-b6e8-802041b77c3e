# 环境变量配置示例
# 复制此文件为 .env 并根据实际情况修改配置值

# ===================
# 基础配置
# ===================

# 生产环境标志 (true/false)
PRODUCTION=false

# 应用密钥 (生产环境必须修改)
SECRET_KEY=your-secret-key-here

# ===================
# 数据库配置
# ===================

# 数据库连接URL
# SQLite: sqlite:///./test.db
# PostgreSQL: postgresql://user:password@localhost/dbname
# MySQL: mysql://user:password@localhost/dbname
DATABASE_URL=sqlite:///./test.db

# ===================
# 管理员配置
# ===================

# 管理员用户名
ADMIN_USERNAME=admin

# 管理员密码 (生产环境必须修改)
ADMIN_PASSWORD=admin123

# 管理员API令牌 (生产环境必须修改)
ADMIN_TOKEN=admin_secret_token_2024

# ===================
# 安全配置
# ===================

# 速率限制 - 每分钟最大请求数
RATE_LIMIT_REQUESTS=100

# 速率限制 - 时间窗口(秒)
RATE_LIMIT_WINDOW=60

# 最大登录尝试次数
MAX_LOGIN_ATTEMPTS=5

# 登录锁定持续时间(秒) - 默认15分钟
LOGIN_LOCKOUT_DURATION=900

# ===================
# AI API配置
# ===================

# DeepSeek API密钥 (必填)
DEEPSEEK_API_KEY=your-deepseek-api-key-here

# DeepSeek API基础URL
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# DeepSeek模型名称
DEEPSEEK_MODEL=deepseek-chat

# ===================
# 缓存配置
# ===================

# 缓存过期时间(秒) - 默认5分钟
CACHE_TTL=300

# 缓存最大条目数
CACHE_MAX_SIZE=1000

# ===================
# 文件上传配置
# ===================

# 最大文件大小(字节) - 默认10MB
MAX_FILE_SIZE=10485760

# 允许的文件类型(逗号分隔)
ALLOWED_FILE_TYPES=json,txt,csv

# ===================
# 日志配置
# ===================

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=app.log

# ===================
# CORS配置
# ===================

# 允许的源域名(逗号分隔) - 生产环境应设置具体域名
# 开发环境: *
# 生产环境: https://yourdomain.com,https://www.yourdomain.com
ALLOWED_ORIGINS=*

# ===================
# 生产环境安全建议
# ===================

# 1. 修改所有默认密码和令牌
# 2. 使用强密码和随机生成的密钥
# 3. 设置具体的CORS域名
# 4. 启用HTTPS
# 5. 定期更新依赖包
# 6. 监控日志文件
# 7. 备份数据库