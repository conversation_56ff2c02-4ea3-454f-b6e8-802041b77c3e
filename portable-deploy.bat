@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    高中信息技术选择题练习平台
echo    便携式部署脚本 v6.0
echo ========================================
echo.

:: 设置变量
set "PYTHON_VERSION=3.10.11"
set "NODE_VERSION=18.18.0"
set "PROJECT_DIR=%~dp0"
set "VENV_DIR=%PROJECT_DIR%venv"

echo 📁 项目目录：%PROJECT_DIR%
echo.

:: 检查Python
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，正在下载安装...
    echo 📥 下载Python %PYTHON_VERSION%...
    
    :: 创建临时目录
    if not exist "%PROJECT_DIR%temp" mkdir "%PROJECT_DIR%temp"
    
    :: 下载Python
    powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/%PYTHON_VERSION%/python-%PYTHON_VERSION%-amd64.exe' -OutFile '%PROJECT_DIR%temp\python-installer.exe'}"
    
    if exist "%PROJECT_DIR%temp\python-installer.exe" (
        echo 🔧 安装Python...
        "%PROJECT_DIR%temp\python-installer.exe" /quiet InstallAllUsers=0 PrependPath=1 Include_test=0
        
        :: 等待安装完成
        timeout /t 30 /nobreak >nul
        
        :: 刷新环境变量
        call refreshenv >nul 2>&1
    ) else (
        echo ❌ Python下载失败，请手动安装Python 3.10+
        echo 下载地址：https://www.python.org/downloads/
        pause
        exit /b 1
    )
) else (
    echo ✅ Python环境检查通过
)

:: 检查Node.js
echo [2/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，正在下载安装...
    echo 📥 下载Node.js %NODE_VERSION%...
    
    :: 下载Node.js
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v%NODE_VERSION%/node-v%NODE_VERSION%-x64.msi' -OutFile '%PROJECT_DIR%temp\nodejs-installer.msi'}"
    
    if exist "%PROJECT_DIR%temp\nodejs-installer.msi" (
        echo 🔧 安装Node.js...
        msiexec /i "%PROJECT_DIR%temp\nodejs-installer.msi" /quiet
        
        :: 等待安装完成
        timeout /t 30 /nobreak >nul
        
        :: 刷新环境变量
        call refreshenv >nul 2>&1
    ) else (
        echo ❌ Node.js下载失败，请手动安装Node.js 18+
        echo 下载地址：https://nodejs.org/
        pause
        exit /b 1
    )
) else (
    echo ✅ Node.js环境检查通过
)

:: 创建Python虚拟环境
echo [3/6] 设置Python虚拟环境...
if not exist "%VENV_DIR%" (
    echo 🔧 创建虚拟环境...
    python -m venv "%VENV_DIR%"
)

:: 激活虚拟环境
call "%VENV_DIR%\Scripts\activate.bat"

:: 安装Python依赖
echo [4/6] 安装Python依赖...
cd /d "%PROJECT_DIR%backend"
pip install --upgrade pip
pip install -r requirements.txt

:: 构建前端
echo [5/6] 构建前端...
cd /d "%PROJECT_DIR%frontend"
call npm install
call npm run build

:: 复制前端构建文件到后端静态目录
echo 📁 复制前端文件...
if not exist "%PROJECT_DIR%backend\static" mkdir "%PROJECT_DIR%backend\static"
xcopy /E /Y "%PROJECT_DIR%frontend\dist\*" "%PROJECT_DIR%backend\static\"

:: 创建数据目录
if not exist "%PROJECT_DIR%data" mkdir "%PROJECT_DIR%data"

:: 启动应用
echo [6/6] 启动应用...
cd /d "%PROJECT_DIR%backend"

echo.
echo ========================================
echo ✅ 环境准备完成！正在启动应用...
echo ========================================
echo.

:: 启动后端服务
start "后端服务" cmd /k "call "%VENV_DIR%\Scripts\activate.bat" && python main.py"

:: 等待服务启动
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo ✅ 应用启动完成！
echo.
echo 🌐 访问地址：http://localhost:8000
echo 📊 管理后台：http://localhost:8000/admin
echo.
echo 💡 使用说明：
echo    - 学生答题：直接访问主页面
echo    - 教师管理：访问 /admin 页面
echo.
echo 🔧 注意事项：
echo    - 关闭此窗口将停止服务
echo    - 数据保存在 data 目录中
echo    - 重新部署请运行此脚本
echo ========================================
echo.

:: 清理临时文件
if exist "%PROJECT_DIR%temp" rmdir /s /q "%PROJECT_DIR%temp"

echo 按任意键打开浏览器访问应用...
pause >nul
start http://localhost:8000